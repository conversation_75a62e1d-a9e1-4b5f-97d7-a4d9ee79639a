#!/usr/bin/env python3
"""
Test script to compare preview text wrapping simulation with actual PDF output.
"""

from stitky import simulate_text_wrapping_for_preview, draw_text_with_breaks
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm

def test_preview_vs_pdf():
    """Test that preview simulation matches PDF behavior."""
    
    # Test cases with various text lengths
    test_cases = [
        "Krátký text",
        "Středně dlouhý text který by se mohl rozd<PERSON>",
        "Velmi dlouhý text s mnoha slovy které by se měly rozdělit na více řádků když je text příliš dlouhý pro jeden řádek",
        "Text-s-pomlčkami-a-dlouhými-slovy-které-nemohou-být-rozděleny",
        "Komb<PERSON><PERSON> normálních slov a velmi-dlouhých-slov-s-pomlčkami které testují různ<PERSON> scé<PERSON>"
    ]
    
    # Simulate label dimensions
    label_width_px = 200  # Preview width in pixels
    font_size = 10
    
    print("Testing Preview vs PDF Text Wrapping")
    print("=" * 50)
    
    for i, text in enumerate(test_cases):
        print(f"\nTest Case {i+1}: {text[:50]}...")
        
        # Get preview simulation
        preview_lines = simulate_text_wrapping_for_preview(text, label_width_px, font_size)
        
        print(f"Preview simulation ({len(preview_lines)} lines):")
        for j, line in enumerate(preview_lines):
            print(f"  {j+1}: '{line}'")
        
        # For PDF comparison, we'd need to actually render and measure
        # But we can at least show what the function would receive
        print(f"PDF would receive: '{text}'")
        print(f"PDF max_width: ~{label_width_px * 0.75:.1f} points")  # Rough conversion
    
    print("\n" + "=" * 50)
    print("Test completed. Check the output above to see if preview")
    print("simulation produces reasonable line breaks that should")
    print("match the PDF behavior.")

def create_comparison_pdf():
    """Create a PDF to visually compare with preview."""
    
    c = canvas.Canvas("test_preview_vs_pdf.pdf", pagesize=A4)
    width, height = A4
    
    # Set font
    c.setFont("Helvetica", 10)
    
    # Title
    c.setFont("Helvetica-Bold", 16)
    c.drawString(50, height - 50, "Preview vs PDF Comparison Test")
    
    # Test text
    test_text = "Velmi dlouhý český text s diakritikou který by se měl rozdělit na více řádků když je omezen šířkou štítku a měl by správně fungovat s českými znaky a pomlčkami."
    
    # Draw box for constraint
    box_x = 50
    box_y = height - 150
    box_width = 200
    box_height = 100
    
    c.rect(box_x, box_y - box_height, box_width, box_height, stroke=1, fill=0)
    
    # Draw text with PDF function
    c.setFont("Helvetica", 10)
    final_y = draw_text_with_breaks(
        c, 
        test_text, 
        box_x + 5, 
        box_y - 15, 
        box_width - 10, 
        12, 
        "Helvetica", 
        10
    )
    
    # Show preview simulation result
    c.setFont("Helvetica-Bold", 12)
    c.drawString(box_x, box_y - box_height - 30, "Preview simulation would show:")
    
    preview_lines = simulate_text_wrapping_for_preview(test_text, box_width - 10, 10)
    
    c.setFont("Helvetica", 10)
    sim_y = box_y - box_height - 50
    for i, line in enumerate(preview_lines):
        c.drawString(box_x + 10, sim_y - (i * 12), f"{i+1}: {line}")
    
    c.save()
    print("Comparison PDF created: test_preview_vs_pdf.pdf")

if __name__ == "__main__":
    test_preview_vs_pdf()
    create_comparison_pdf()
