#!/usr/bin/env python3
"""
Debug script to test actual label drawing with real dimensions and data.
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from stitky import draw_label, ROW_HEIGHT, LABEL_WIDTH

def debug_real_labels():
    """Debug actual label drawing with real dimensions."""
    
    c = canvas.Canvas("debug_real_labels.pdf", pagesize=A4)
    width, height = A4
    
    # Set font
    c.setFont("Helvetica", 10)
    
    # Title
    c.setFont("Helvetica-Bold", 16)
    c.drawString(50, height - 50, "Debug: Real Label Dimensions Test")
    
    # Show the actual dimensions being used
    c.setFont("Helvetica", 10)
    c.drawString(50, height - 80, f"ROW_HEIGHT = {ROW_HEIGHT/mm:.1f} mm ({ROW_HEIGHT:.1f} points)")
    c.drawString(50, height - 100, f"LABEL_WIDTH = {LABEL_WIDTH/mm:.1f} mm ({LABEL_WIDTH:.1f} points)")
    
    # Test with very long text that should definitely wrap
    test_labels = [
        {
            "nazev": "Velmi dlouhý název organizace s mnoha slovy které by se měly rozdělit na více řádků když je text příliš dlouhý pro jeden řádek",
            "ulice": "Dlouhá-velmi-dlouhá-ulice-s-pomlčkami-a-čísly 123/456/789",
            "mesto_psc": "Město-s-velmi-dlouhým-názvem 12345-67890",
            "y": height - 150
        },
        {
            "nazev": "Krátký název",
            "ulice": "Krátká ulice 1",
            "mesto_psc": "Praha 11000",
            "y": height - 300
        }
    ]
    
    for i, label_data in enumerate(test_labels):
        # Draw label boundary to visualize
        label_x = 50
        label_y = label_data["y"]
        
        c.setStrokeColorRGB(1, 0, 0)  # Red border
        c.setLineWidth(2)
        c.rect(label_x, label_y - ROW_HEIGHT, LABEL_WIDTH, ROW_HEIGHT, stroke=1, fill=0)
        
        # Add label info
        c.setFont("Helvetica-Bold", 8)
        c.setFillColorRGB(1, 0, 0)
        c.drawString(label_x + 5, label_y + 10, f"Label {i+1} - Real dimensions")
        
        # Reset for label text
        c.setFillColorRGB(0, 0, 0)
        c.setFont("Helvetica", 10)
        
        # Draw the actual label
        print(f"Drawing label {i+1}:")
        print(f"  Position: x={label_x + 5}, y={label_y - 15}")
        print(f"  Width: {LABEL_WIDTH - 10}")
        print(f"  Height: {ROW_HEIGHT - 20}")
        print(f"  Název: {label_data['nazev']}")
        print(f"  Ulice: {label_data['ulice']}")
        print(f"  Město+PSČ: {label_data['mesto_psc']}")
        
        final_y = draw_label(
            c,
            label_data["nazev"],
            label_data["ulice"], 
            label_data["mesto_psc"],
            label_x + 5,
            label_y - 15,
            LABEL_WIDTH - 10,
            "Helvetica",
            10,
            ROW_HEIGHT - 20
        )
        
        # Show where text ended
        c.setStrokeColorRGB(0, 0, 1)  # Blue line
        c.line(label_x, final_y, label_x + LABEL_WIDTH, final_y)
        c.setFont("Helvetica", 8)
        c.drawString(label_x + LABEL_WIDTH + 10, final_y, f"Text ended at y={final_y:.1f}")
        
        # Show the bottom boundary
        bottom_boundary = label_y - ROW_HEIGHT + 5
        c.setStrokeColorRGB(0, 1, 0)  # Green line
        c.line(label_x, bottom_boundary, label_x + LABEL_WIDTH, bottom_boundary)
        c.drawString(label_x + LABEL_WIDTH + 10, bottom_boundary, f"Bottom boundary y={bottom_boundary:.1f}")
        
        print(f"  Final Y: {final_y}")
        print(f"  Bottom boundary: {bottom_boundary}")
        print()
    
    c.save()
    print("Debug PDF created: debug_real_labels.pdf")

if __name__ == "__main__":
    debug_real_labels()
