#!/usr/bin/env python3
"""
Final comprehensive test to verify both PDF and preview work correctly
with proper text wrapping and boundary checking.
"""

import pandas as pd
from stitky import create_labels

def create_test_data():
    """Create test data with various text lengths to test wrapping."""
    
    test_data = [
        {
            "Krátký název": "Krátký název",
            "Ulice": "Krátká ulice 1",
            "Město": "Praha",
            "PSČ": "11000"
        },
        {
            "Krátký název": "<PERSON>elmi dlouhý název organizace s mnoha slovy které by se měly rozdělit na více řádků",
            "Ulice": "Dlouhá-velmi-dlouhá-ulice-s-pomlčkami-a-čísly 123/456/789",
            "Město": "Město-s-velmi-dlouhým-názvem",
            "PSČ": "12345-67890"
        },
        {
            "Krátký název": "<PERSON><PERSON><PERSON><PERSON><PERSON> dlouhý název organizace",
            "Ulice": "Norm<PERSON>lní ulice 123",
            "<PERSON>ěsto": "<PERSON><PERSON>",
            "PSČ": "60200"
        },
        {
            "Krátký název": "Text-s-pomlčkami-který-nemůže-být-rozdělen",
            "Ulice": "Ulice-také-s-pomlčkami-a-dlouhým-názvem 456",
            "Město": "Ostrava",
            "PSČ": "70000"
        },
        {
            "Krátký název": "Kombinace normálních slov a velmi-dlouhých-slov-s-pomlčkami",
            "Ulice": "Kombinovaná ulice s normálními slovy a dlouhými-názvy-s-pomlčkami 789",
            "Město": "České-Budějovice",
            "PSČ": "37001"
        },
        {
            "Krátký název": "Test prázdných polí",
            "Ulice": "",
            "Město": "Plzeň",
            "PSČ": "30100"
        }
    ]
    
    return pd.DataFrame(test_data)

def run_final_test():
    """Run the final comprehensive test."""
    
    print("Running Final Comprehensive Test")
    print("=" * 40)
    
    # Create test data
    df = create_test_data()
    
    print(f"Created test data with {len(df)} records:")
    for i, row in df.iterrows():
        print(f"  {i+1}. {row['Krátký název'][:30]}...")
    
    # Test PDF generation
    print("\nTesting PDF generation...")
    try:
        create_labels(df, "final_test_output.pdf")
        print("✅ PDF created successfully: final_test_output.pdf")
    except Exception as e:
        print(f"❌ PDF generation failed: {e}")
        return False
    
    print("\nTest Results:")
    print("- PDF should contain 6 labels with various text lengths")
    print("- Long text should wrap to multiple lines")
    print("- Text should not overflow label boundaries")
    print("- Empty fields should be handled gracefully")
    
    print("\nTo verify the results:")
    print("1. Open final_test_output.pdf to check PDF output")
    print("2. Run the main application (python stitky.py) to check preview")
    print("3. Load the test data and compare preview with PDF")
    
    # Save test data for manual testing
    df.to_csv("final_test_data.csv", index=False, encoding='utf-8')
    print("4. Test data saved as: final_test_data.csv")
    
    return True

if __name__ == "__main__":
    success = run_final_test()
    if success:
        print("\n🎉 Final test completed successfully!")
        print("Both PDF generation and preview should now work correctly")
        print("with proper text wrapping and boundary checking.")
    else:
        print("\n❌ Final test failed. Please check the errors above.")
