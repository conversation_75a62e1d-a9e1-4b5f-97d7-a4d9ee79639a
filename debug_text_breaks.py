#!/usr/bin/env python3
"""
Debug script to test if draw_text_with_breaks is actually working correctly.
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from stitky import draw_text_with_breaks

def debug_text_breaks():
    """Debug the text breaking functionality."""
    
    c = canvas.Canvas("debug_text_breaks.pdf", pagesize=A4)
    width, height = A4
    
    # Set font
    c.setFont("Helvetica", 10)
    
    # Title
    c.setFont("Helvetica-Bold", 16)
    c.drawString(50, height - 50, "Debug: Text Breaking Test")
    
    # Test with a long sentence that should definitely wrap
    test_text = "This is a very long sentence that should definitely wrap across multiple lines when the width is constrained to a small area like this label width."
    
    # Draw a box to show the constraint
    box_x = 50
    box_y = height - 150
    box_width = 150  # Small width to force wrapping
    box_height = 100
    
    c.rect(box_x, box_y - box_height, box_width, box_height, stroke=1, fill=0)
    
    # Test the function
    c.setFont("Helvetica", 10)
    final_y = draw_text_with_breaks(
        c, 
        test_text, 
        box_x + 5, 
        box_y - 15, 
        box_width - 10, 
        12, 
        "Helvetica", 
        10
    )
    
    # Show where the text ended
    c.setStrokeColorRGB(1, 0, 0)
    c.line(box_x, final_y, box_x + box_width, final_y)
    c.drawString(box_x + box_width + 10, final_y, f"Text ended here (y={final_y:.1f})")
    
    # Test 2: Even longer text
    test_text2 = "Velmi dlouhý český text s diakritikou který by se měl rozdělit na více řádků když je omezen šířkou štítku a měl by správně fungovat s českými znaky."
    
    box2_y = height - 300
    c.setStrokeColorRGB(0, 0, 0)
    c.rect(box_x, box2_y - box_height, box_width, box_height, stroke=1, fill=0)
    
    final_y2 = draw_text_with_breaks(
        c, 
        test_text2, 
        box_x + 5, 
        box2_y - 15, 
        box_width - 10, 
        12, 
        "Helvetica", 
        10
    )
    
    c.setStrokeColorRGB(1, 0, 0)
    c.line(box_x, final_y2, box_x + box_width, final_y2)
    c.drawString(box_x + box_width + 10, final_y2, f"Text ended here (y={final_y2:.1f})")
    
    # Test 3: Test with boundary limits
    test_text3 = "This text should be truncated when it hits the boundary limit that we set for the label height."
    
    box3_y = height - 450
    box3_height = 50  # Smaller height to test truncation
    c.setStrokeColorRGB(0, 0, 0)
    c.rect(box_x, box3_y - box3_height, box_width, box3_height, stroke=1, fill=0)
    
    # Calculate min_y for boundary
    min_y = box3_y - box3_height + 12
    
    final_y3 = draw_text_with_breaks(
        c, 
        test_text3, 
        box_x + 5, 
        box3_y - 15, 
        box_width - 10, 
        12, 
        "Helvetica", 
        10,
        min_y  # This should limit the text
    )
    
    c.setStrokeColorRGB(1, 0, 0)
    c.line(box_x, final_y3, box_x + box_width, final_y3)
    c.drawString(box_x + box_width + 10, final_y3, f"Text ended here (y={final_y3:.1f})")
    c.drawString(box_x + box_width + 10, min_y, f"Boundary was at (y={min_y:.1f})")
    
    c.save()
    print("Debug PDF created: debug_text_breaks.pdf")

if __name__ == "__main__":
    debug_text_breaks()
