import pandas as pd
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from reportlab.pdfgen import canvas
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
import math

# Global label parameters
A4_WIDTH, A4_HEIGHT = A4  # A4 dimensions
MARGIN = 0 * mm  # Space between labels
ROW_GAP = 0 * mm  # Vertical gap between rows
ROW_HEIGHT = 37 * mm  # Height of a single label
LABELS_PER_ROW = 3  # Number of labels in a row

# Calculate available width and label width
AVAILABLE_WIDTH = A4_WIDTH - (LABELS_PER_ROW - 1) * MARGIN
LABEL_WIDTH = AVAILABLE_WIDTH / LABELS_PER_ROW  # Dynamic label width

# Calculate max rows per page
MAX_ROWS_PER_PAGE = int(A4_HEIGHT // (ROW_HEIGHT + ROW_GAP))


# Load data from Excel
def load_data(excel_file):
    df = pd.read_excel(excel_file)
    return df


# Draw text with automatic line breaks
def draw_text_with_breaks(c, text, x, y, max_width, line_height, font_name='Helvetica', font_size=10, min_y=None):
    """
    Draw text with automatic line breaks. Moves long words to new lines instead of breaking them. Handles long words by breaking them if necessary.

    Args:
        c: Canvas object
        text: Text to draw
        x, y: Starting position
        max_width: Maximum width for text
        line_height: Height between lines
        font_name: Font name to use
        font_size: Font size to use
        min_y: Minimum y position (bottom boundary) - stops drawing if exceeded

    Returns:
        Final y position after drawing all text
    """
    if not text or text.strip() == '':
        return y

    # Clean the text - remove extra whitespace and normalize
    text = ' '.join(text.strip().split())
    words = text.split(' ')
    line = ''

    for word in words:
        # Test if adding this word would exceed the width
        test_line = (line + ' ' + word).strip() if line else word
        width = c.stringWidth(test_line, font_name, font_size)

        if width > max_width and line:
            # Current line is full, draw it and start new line with the current word
            c.drawString(x, y, line.strip())
            y -= line_height

            # Check if we would exceed the bottom boundary
            if min_y is not None and y < min_y:
                return y  # Stop drawing, we've reached the boundary

            line = word
        elif width > max_width and not line:
            # Single word is too long for the line, but still place it on its own line
            # This handles cases where even a single word exceeds max_width
            c.drawString(x, y, word)
            y -= line_height

            # Check if we would exceed the bottom boundary
            if min_y is not None and y < min_y:
                return y  # Stop drawing, we've reached the boundary

            line = ''
        else:
            # Word fits, add it to current line
            line = test_line

    # Draw any remaining text in the last line
    if line and line.strip():
        # Check if we have space for this final line
        if min_y is None or y >= min_y:
            c.drawString(x, y, line.strip())

    return y  # Return the last y position after drawing




def draw_label(c, nazev, ulice, mesto_psc, x, y, width, font_name='Helvetica', font_size=10, label_height=None):
    line_height = 12  # Set height for each text line

    # Calculate the bottom boundary of the label to prevent overflow
    min_y = None
    if label_height is not None:
        min_y = y - label_height + line_height  # Leave space for at least one line

    # Draw Název with line wrapping
    y = draw_text_with_breaks(c, nazev, x, y, width, line_height, font_name, font_size, min_y)

    # After drawing Název, spacing down for Ulice
    y -= line_height  # Additional space before Ulice

    # Check if we still have space
    if min_y is not None and y < min_y:
        return y  # Stop here, no more space

    # Draw Ulice with line wrapping
    y = draw_text_with_breaks(c, ulice, x, y, width, line_height, font_name, font_size, min_y)

    # After drawing Ulice, spacing down before Město and PSČ
    y -= line_height  # Additional space before Město + PSČ

    # Check if we still have space
    if min_y is not None and y < min_y:
        return y  # Stop here, no more space

    # Draw Město and PSČ with line wrapping
    draw_text_with_breaks(c, mesto_psc, x, y, width, line_height, font_name, font_size, min_y)

    return y


# Create a PDF with labels
def create_labels(data, output_file):
    c = canvas.Canvas(output_file, pagesize=A4)

    # Try to register custom font, fall back to Helvetica if not available
    font_name = 'Helvetica'
    try:
        if os.path.exists('DejaVuSans.ttf'):
            pdfmetrics.registerFont(TTFont('CzechFont', 'DejaVuSans.ttf'))
            font_name = 'CzechFont'
    except:
        pass  # Use default font

    c.setFont(font_name, 10)  # Set the font for the canvas

    # Initialize position counters
    current_row = 0  # Current row in label grid

    for i, row in data.iterrows():
        if current_row >= MAX_ROWS_PER_PAGE:
            c.showPage()  # Create a new page
            c.setFont(font_name, 10)  # Set the font on the new page again
            current_row = 0  # Reset row counter for the new page

        # Calculate the position of the label
        column_index = i % LABELS_PER_ROW
        y = A4_HEIGHT - (current_row + 1) * ROW_HEIGHT - current_row * ROW_GAP

        # Calculate x position based on the column index
        x = column_index * (LABEL_WIDTH + MARGIN) + MARGIN

        # Call the draw_label function to print the fields with line breaks
        mesto_psc = f"{row['Město']} {row['PSČ']}"  # Combine city and postal code
        draw_label(c, str(row['Krátký název']), str(row['Ulice']), mesto_psc, x + 2 * mm, y + 28 * mm, LABEL_WIDTH - 4 * mm, font_name, 10, ROW_HEIGHT - 4 * mm)

        # Move to the next row at the end of the current row
        if column_index == LABELS_PER_ROW - 1:  # If the last column of the row is filled
            current_row += 1

    c.save()  # Save the PDF

class LabelGeneratorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Label Layout Designer")
        self.root.geometry("1400x900")

        self.df = None
        self.pages = []  # List of pages, each page contains label positions
        self.current_page = 0
        self.labels_per_row = 3
        self.max_labels_per_page = MAX_ROWS_PER_PAGE * self.labels_per_row  # 7 rows * 3 columns
        self.data_loaded = False
        self.label_rectangles = {}  # Canvas items for current page

        self.setup_ui()
        self.initialize_pages()

    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # File selection frame
        file_frame = ttk.Frame(main_frame)
        file_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)

        ttk.Label(file_frame, text="Excel/CSV File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))

        self.file_path_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_path_var, state="readonly")
        self.file_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))

        ttk.Button(file_frame, text="Open File", command=self.browse_file).grid(row=0, column=2)

        # Control buttons frame
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))

        # Page navigation
        page_frame = ttk.Frame(control_frame)
        page_frame.pack(side=tk.LEFT)

        ttk.Button(page_frame, text="◀ Prev Page", command=self.prev_page).pack(side=tk.LEFT, padx=(0, 5))
        self.page_label = ttk.Label(page_frame, text="Page 1 of 1")
        self.page_label.pack(side=tk.LEFT, padx=(5, 5))
        ttk.Button(page_frame, text="Next Page ▶", command=self.next_page).pack(side=tk.LEFT, padx=(5, 0))

        # Action buttons
        action_frame = ttk.Frame(control_frame)
        action_frame.pack(side=tk.RIGHT)

        self.show_data_btn = ttk.Button(action_frame, text="Show Data", command=self.show_data, state="disabled")
        self.show_data_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.toggle_data_btn = ttk.Button(action_frame, text="Hide Data", command=self.toggle_data_visibility, state="disabled")
        self.toggle_data_btn.pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(action_frame, text="Export PDF", command=self.export_pdf).pack(side=tk.LEFT)

        # Status info
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        self.status_label = ttk.Label(status_frame, text="Load a file to start designing your label layout")
        self.status_label.pack(side=tk.LEFT)

        # Legend
        legend_frame = ttk.Frame(status_frame)
        legend_frame.pack(side=tk.RIGHT)

        ttk.Label(legend_frame, text="Legend:", font=('Arial', 9, 'bold')).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Label(legend_frame, text="🔵 Will print", foreground='blue').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Label(legend_frame, text="🟢 With data", foreground='green').pack(side=tk.LEFT, padx=(0, 10))
        ttk.Label(legend_frame, text="⚪ Hidden/Empty", foreground='gray').pack(side=tk.LEFT)

        # Preview canvas
        preview_frame = ttk.LabelFrame(main_frame, text="Paper Layout - Click labels to show/hide", padding="5")
        preview_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        main_frame.rowconfigure(3, weight=1)

        canvas_frame = ttk.Frame(preview_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        self.preview_canvas = tk.Canvas(canvas_frame, bg='white', width=600, height=700)
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.preview_canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.preview_canvas.xview)

        self.preview_canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        self.preview_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

    def initialize_pages(self):
        """Initialize with one empty page"""
        self.pages = [[False] * self.max_labels_per_page]  # False = empty, True = has label
        self.current_page = 0
        self.draw_current_page()

    def browse_file(self):
        file_path = filedialog.askopenfilename(
            title="Select Excel or CSV File",
            filetypes=[("CSV files", "*.csv"), ("Excel files", "*.xlsx"), ("All files", "*.*")]
        )
        if file_path:
            self.file_path_var.set(file_path)
            self.load_file(file_path)

    def load_file(self, file_path):
        try:
            if file_path.endswith('.xlsx'):
                self.df = pd.read_excel(file_path)
            else:
                self.df = pd.read_csv(file_path)

            # Calculate required pages based on data
            total_labels = len(self.df)
            required_pages = math.ceil(total_labels / self.max_labels_per_page)

            # Initialize pages with empty labels
            self.pages = [[False] * self.max_labels_per_page for _ in range(required_pages)]

            # Fill labels sequentially by default
            for i in range(total_labels):
                page_idx = i // self.max_labels_per_page
                label_idx = i % self.max_labels_per_page
                self.pages[page_idx][label_idx] = True

            self.current_page = 0
            self.data_loaded = False
            self.show_data_btn.config(state="normal")
            self.draw_current_page()
            self.update_page_label()
            self.update_status(f"Loaded {total_labels} labels. Click positions to hide/show labels, then 'Show Data' to preview.")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load file: {str(e)}")

    def draw_current_page(self):
        """Draw the current page with empty or filled labels"""
        self.preview_canvas.delete("all")
        self.label_rectangles = {}

        if not self.pages:
            return

        # Paper dimensions (A4 in mm converted to pixels for display)
        scale = 2.5
        paper_width_mm = A4_WIDTH / mm
        paper_height_mm = A4_HEIGHT / mm
        paper_width_px = paper_width_mm * scale
        paper_height_px = paper_height_mm * scale

        # Set canvas scroll region
        self.preview_canvas.configure(scrollregion=(0, 0, paper_width_px, paper_height_px))

        # Draw paper background
        self.preview_canvas.create_rectangle(0, 0, paper_width_px, paper_height_px,
                                           fill='white', outline='black', width=2)

        # Label dimensions (using global constants)
        margin_mm = MARGIN / mm  # Convert to mm
        row_gap_mm = ROW_GAP / mm  # Convert to mm
        label_height_mm = ROW_HEIGHT / mm  # Convert to mm
        label_width_mm = LABEL_WIDTH / mm  # Convert to mm

        # Convert to pixels
        margin_px = margin_mm * scale
        label_width_px = label_width_mm * scale
        label_height_px = label_height_mm * scale

        # Draw labels
        current_page_labels = self.pages[self.current_page]

        for i in range(self.max_labels_per_page):
            row = i // LABELS_PER_ROW
            col = i % LABELS_PER_ROW

            if row >= MAX_ROWS_PER_PAGE:
                break

            # Calculate position
            x_mm = col * label_width_mm
            y_mm = row * (label_height_mm + row_gap_mm)

            x_px = x_mm * scale
            y_px = y_mm * scale

            # Determine label appearance
            has_label = current_page_labels[i]



            if has_label:
                if not self.data_loaded:
                    fill_color = 'lightblue'
                    outline_color = 'blue'
                    text_color = 'black'
                else:
                    fill_color = 'lightgreen'
                    outline_color = 'darkgreen'
                    text_color = 'black'
            else:
                fill_color = 'lightgray'
                outline_color = 'darkgray'
                text_color = 'gray'

            # Draw label rectangle
            rect_id = self.preview_canvas.create_rectangle(
                x_px, y_px, x_px + label_width_px, y_px + label_height_px,
                fill=fill_color, outline=outline_color, width=2
            )

            self.label_rectangles[i] = rect_id

            # Add label content
            if has_label and self.data_loaded and self.df is not None:
                # Show actual data
                data_index = self.get_data_index_for_position(i)
                if data_index < len(self.df):
                    row_data = self.df.iloc[data_index]
                    kratky_nazev = str(row_data.get('Krátký název', ''))
                    ulice = str(row_data.get('Ulice', ''))
                    mesto = str(row_data.get('Město', ''))
                    psc = str(row_data.get('PSČ', ''))

                    # Create text (scaled for display)
                    text_size = max(6, int(8 * scale / 2))
                    self.preview_canvas.create_text(
                        x_px + 5, y_px + 5,
                        text=kratky_nazev,
                        anchor='nw',
                        font=('Arial', text_size, 'bold'),
                        fill=text_color,
                        width=label_width_px - 10
                    )
                    self.preview_canvas.create_text(
                        x_px + 5, y_px + 25,
                        text=ulice,
                        anchor='nw',
                        font=('Arial', text_size),
                        fill=text_color,
                        width=label_width_px - 10
                    )
                    self.preview_canvas.create_text(
                        x_px + 5, y_px + 45,
                        text=f"{mesto} {psc}",
                        anchor='nw',
                        font=('Arial', text_size),
                        fill=text_color,
                        width=label_width_px - 10
                    )
            elif has_label:
                # Show placeholder for label that will contain data
                position_text = f"LABEL #{i+1}\n(Click to hide here)"
                self.preview_canvas.create_text(
                    x_px + label_width_px/2, y_px + label_height_px/2,
                    text=position_text,
                    anchor='center',
                    font=('Arial', 9, 'bold'),
                    fill=text_color
                )
            else:
                # Show empty placeholder - this position will not have a label
                position_text = f"HIDDEN #{i+1}\n(Click to show here)"
                self.preview_canvas.create_text(
                    x_px + label_width_px/2, y_px + label_height_px/2,
                    text=position_text,
                    anchor='center',
                    font=('Arial', 8),
                    fill=text_color
                )

            # Bind click event
            self.preview_canvas.tag_bind(rect_id, "<Button-1>",
                                       lambda event, idx=i: self.on_label_click(idx))

    def get_data_index_for_position(self, position):
        """Get the data index for a given position across all pages"""
        data_index = 0
        # Count all active labels before this position
        for page_idx in range(len(self.pages)):
            for pos in range(self.max_labels_per_page):
                if self.pages[page_idx][pos]:  # Only count active (non-hidden) labels
                    if page_idx == self.current_page and pos == position:
                        return data_index
                    data_index += 1
        return data_index

    def on_label_click(self, position):
        """Handle clicking on a label position"""
        if self.data_loaded:
            self.update_status("Cannot rearrange labels while data is visible. Click 'Hide Data' first.")
            return  # Don't allow changes when data is loaded

        current_page_labels = self.pages[self.current_page]

        if current_page_labels[position]:
            # Hide label at this position - the data moves to the end
            current_page_labels[position] = False
            self.move_label_to_end()
            self.update_status(f"Position {position + 1} hidden. Label data moved to end.")
        else:
            # Show label at this position - take data from the last available position
            self.move_label_from_last_available(position)
            self.update_status(f"Position {position + 1} now shows a label.")
        self.draw_current_page()

    def move_label_to_end(self):
        """Move a label to the last position of the last page, or create new page if needed"""
        # Check if last page has space at the end
        if self.pages:
            last_page = self.pages[-1]
            # Find the last position in the last page
            for pos in range(self.max_labels_per_page - 1, -1, -1):
                if not last_page[pos]:
                    last_page[pos] = True
                    return

        # If no space at end of last page, create new page
        new_page = [False] * self.max_labels_per_page
        new_page[0] = True
        self.pages.append(new_page)
        self.update_page_label()

    def move_label_from_last_available(self, target_position):
        """Move a label from the last available position to target position"""
        # Find last filled position (search from end backwards)
        for page_idx in range(len(self.pages) - 1, -1, -1):
            for pos in range(self.max_labels_per_page - 1, -1, -1):
                if self.pages[page_idx][pos]:
                    # Remove from last position and add to target position
                    self.pages[page_idx][pos] = False
                    self.pages[self.current_page][target_position] = True

                    # Remove empty pages at the end
                    while len(self.pages) > 1 and not any(self.pages[-1]):
                        self.pages.pop()

                    self.update_page_label()
                    return

    def prev_page(self):
        if self.current_page > 0:
            self.current_page -= 1
            self.draw_current_page()
            self.update_page_label()

    def next_page(self):
        if self.current_page < len(self.pages) - 1:
            self.current_page += 1
            self.draw_current_page()
            self.update_page_label()

    def update_page_label(self):
        total_pages = len(self.pages)
        self.page_label.config(text=f"Page {self.current_page + 1} of {total_pages}")

    def update_status(self, message):
        """Update the status label"""
        self.status_label.config(text=message)

    def show_data(self):
        """Load and show data in the labels"""
        if self.df is None:
            messagebox.showerror("Error", "Please load a file first.")
            return

        self.data_loaded = True
        self.show_data_btn.config(state="disabled")
        self.toggle_data_btn.config(state="normal", text="Hide Data")
        self.draw_current_page()
        self.update_status("Data loaded! Green positions show actual data. Gray positions will be empty in PDF.")

    def toggle_data_visibility(self):
        """Toggle between showing and hiding data"""
        self.data_loaded = not self.data_loaded

        if self.data_loaded:
            self.toggle_data_btn.config(text="Hide Data")
            self.show_data_btn.config(state="disabled")
            self.update_status("Data visible! Green=will print, Gray=empty. Click 'Hide Data' to rearrange.")
        else:
            self.toggle_data_btn.config(text="Show Data")
            self.show_data_btn.config(state="normal")
            self.update_status("Data hidden! Blue=will print, Gray=hidden. Click positions to hide/show labels.")

        self.draw_current_page()

    def export_pdf(self):
        """Export PDF with current label arrangement"""
        if self.df is None:
            messagebox.showerror("Error", "Please load a file first.")
            return

        # Ask for output file
        output_file = filedialog.asksaveasfilename(
            title="Save PDF As",
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )

        if output_file:
            try:
                self.create_custom_pdf(output_file)
                messagebox.showinfo("Success", f"PDF created successfully: {output_file}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to generate PDF: {str(e)}")

    def create_custom_pdf(self, output_file):
        """Create PDF with custom label arrangement"""
        # Check if DejaVuSans.ttf exists
        font_path = "DejaVuSans.ttf"
        use_custom_font = os.path.exists(font_path)

        if use_custom_font:
            try:
                pdfmetrics.registerFont(TTFont('CzechFont', font_path))
            except:
                use_custom_font = False

        # Create PDF
        c = canvas.Canvas(output_file, pagesize=A4)
        width, height = A4

        if use_custom_font:
            c.setFont('CzechFont', 10)
            font_name = 'CzechFont'
        else:
            c.setFont('Helvetica', 10)
            font_name = 'Helvetica'

        # Use global label dimensions

        data_index = 0

        for page_idx, page_labels in enumerate(self.pages):
            if page_idx > 0:
                c.showPage()
                if use_custom_font:
                    c.setFont('CzechFont', 10)
                else:
                    c.setFont('Helvetica', 10)

            for pos in range(self.max_labels_per_page):
                if page_labels[pos] and data_index < len(self.df):
                    # Calculate position
                    row = pos // LABELS_PER_ROW
                    col = pos % LABELS_PER_ROW

                    x = (col * LABEL_WIDTH) + 4 * mm
                    y = (height - row * ROW_HEIGHT - row * ROW_GAP) - 10 * mm

                    # Get data
                    row_data = self.df.iloc[data_index]
                    kratky_nazev = str(row_data.get('Krátký název', ''))
                    ulice = str(row_data.get('Ulice', ''))
                    mesto = str(row_data.get('Město', ''))
                    psc = str(row_data.get('PSČ', ''))
                    mesto_psc = f"{mesto} {psc}"

                    # Draw label
                    draw_label(c, kratky_nazev, ulice, mesto_psc, x, y, LABEL_WIDTH, font_name, 10, ROW_HEIGHT)

                    data_index += 1

        c.save()


def main():
    root = tk.Tk()
    app = LabelGeneratorGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
