#!/usr/bin/env python3
"""
Test script to demonstrate improved line breaking functionality.
This script creates a test PDF with various text scenarios to verify
that long words are moved to new lines instead of being broken.
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from stitky import draw_text_with_breaks, draw_label

def test_line_breaks():
    """Test the improved line breaking functionality."""
    
    # Create a test PDF
    c = canvas.Canvas("test_line_breaks.pdf", pagesize=A4)
    width, height = A4
    
    # Set font
    c.setFont("Helvetica", 10)
    
    # Test cases
    test_cases = [
        {
            "title": "Normal text with spaces",
            "text": "This is a normal sentence that should wrap nicely across multiple lines when it gets too long for the available width.",
            "x": 50,
            "y": height - 100,
            "max_width": 200
        },
        {
            "title": "Very long word moved to new line",
            "text": "This sentence contains a supercalifragilisticexpialidocious word that will be moved to its own line.",
            "x": 50,
            "y": height - 200,
            "max_width": 200
        },
        {
            "title": "Multiple long words on separate lines",
            "text": "Antidisestablishmentarianism and pneumonoultramicroscopicsilicovolcanoconiosisareextremelylongwords will each get their own lines.",
            "x": 50,
            "y": height - 300,
            "max_width": 200
        },
        {
            "title": "Mixed content with numbers and special chars",
            "text": "Address: Dlouhá-velmi-dlouhá-ulice-s-pomlčkami 123/456, 12345 Město-s-pomlčkami",
            "x": 50,
            "y": height - 400,
            "max_width": 200
        },
        {
            "title": "Empty and whitespace handling",
            "text": "   Text with   extra   spaces   and   tabs\t\tthat   should   be   normalized   ",
            "x": 50,
            "y": height - 500,
            "max_width": 200
        }
    ]
    
    # Draw test cases
    for i, test_case in enumerate(test_cases):
        # Draw title
        c.setFont("Helvetica-Bold", 12)
        c.drawString(test_case["x"], test_case["y"] + 20, f"{i+1}. {test_case['title']}")
        
        # Draw border for max width visualization
        c.rect(test_case["x"], test_case["y"] - 60, test_case["max_width"], 60, stroke=1, fill=0)
        
        # Draw text with line breaks
        c.setFont("Helvetica", 10)
        draw_text_with_breaks(
            c, 
            test_case["text"], 
            test_case["x"] + 5, 
            test_case["y"] - 15, 
            test_case["max_width"] - 10, 
            12, 
            "Helvetica", 
            10
        )
    
    # Test the draw_label function
    c.showPage()
    c.setFont("Helvetica-Bold", 14)
    c.drawString(50, height - 50, "Label Drawing Test")
    
    # Test label with long content
    c.setFont("Helvetica", 10)
    label_x = 50
    label_y = height - 100
    label_width = 180
    
    # Draw border for label
    c.rect(label_x, label_y - 100, label_width, 100, stroke=1, fill=0)
    
    # Test data
    nazev = "Velmi dlouhý název organizace s mnoha slovy které se nemusí vejít na jeden řádek"
    ulice = "Dlouhá-velmi-dlouhá-ulice-s-pomlčkami-a-čísly 123/456"
    mesto_psc = "Město-s-velmi-dlouhým-názvem-který-se-také-nemusí-vejít 12345"
    
    draw_label(c, nazev, ulice, mesto_psc, label_x + 5, label_y - 15, label_width - 10, "Helvetica", 10)
    
    # Save the PDF
    c.save()
    print("Test PDF created: test_line_breaks.pdf")

if __name__ == "__main__":
    test_line_breaks()
