#!/usr/bin/env python3
"""
Test script to verify that text overflow into adjacent labels is fixed.
This creates labels with very long text to test the boundary checking.
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from stitky import draw_label, ROW_HEIGHT, LABEL_WIDTH

def test_overflow_prevention():
    """Test that long text doesn't overflow into adjacent labels."""
    
    c = canvas.Canvas("test_overflow_fix.pdf", pagesize=A4)
    width, height = A4
    
    # Set font
    c.setFont("Helvetica", 10)
    
    # Title
    c.setFont("Helvetica-Bold", 16)
    c.drawString(50, height - 50, "Text Overflow Prevention Test")
    
    # Test with extremely long text that would normally overflow
    test_labels = [
        {
            "nazev": "Velmi dlouhý název organizace s mnoha slovy které by se norm<PERSON>lně nevešly na jeden štítek a pokračovaly by do <PERSON><PERSON><PERSON><PERSON><PERSON> což by bylo ve<PERSON>i <PERSON> pro tisk",
            "ulice": "Dlouhá-velmi-dlouhá-ulice-s-pomlčkami-a-čísly-které-by-také-mohly-způsobit-problémy-s-přetečením-textu 123/456/789",
            "mesto_psc": "Město-s-velmi-dlouhým-názvem-který-by-se-také-nemusel-vejít-na-štítek-a-mohl-by-přetéct 12345-67890",
            "x": 50,
            "y": height - 120
        },
        {
            "nazev": "Další test s dlouhým textem",
            "ulice": "Krátká ulice 1",
            "mesto_psc": "Praha 11000",
            "x": 50,
            "y": height - 220
        },
        {
            "nazev": "Normální název",
            "ulice": "Normální ulice 123",
            "mesto_psc": "Normální město 12345",
            "x": 50,
            "y": height - 320
        }
    ]
    
    # Draw test labels with boundaries
    for i, label in enumerate(test_labels):
        # Draw label boundary rectangle to visualize the limits
        label_height = ROW_HEIGHT
        c.setStrokeColorRGB(1, 0, 0)  # Red border
        c.rect(label["x"], label["y"] - label_height, LABEL_WIDTH, label_height, stroke=1, fill=0)
        
        # Add label number
        c.setFont("Helvetica-Bold", 8)
        c.setFillColorRGB(1, 0, 0)  # Red text
        c.drawString(label["x"] + 5, label["y"] + 5, f"Label {i+1} - Boundary")
        
        # Reset color for label text
        c.setFillColorRGB(0, 0, 0)  # Black text
        c.setFont("Helvetica", 10)
        
        # Draw the label with boundary checking
        draw_label(
            c, 
            label["nazev"], 
            label["ulice"], 
            label["mesto_psc"], 
            label["x"] + 5, 
            label["y"] - 15, 
            LABEL_WIDTH - 10, 
            "Helvetica", 
            10, 
            label_height - 20  # Leave some margin
        )
    
    # Add explanation
    c.setFont("Helvetica", 10)
    c.setFillColorRGB(0, 0, 0)
    explanation = [
        "Test Results:",
        "• Red rectangles show label boundaries",
        "• Text should NOT extend beyond the red borders",
        "• Long text should be truncated to fit within label height",
        "• This prevents text from appearing on adjacent labels when printed"
    ]
    
    y_pos = height - 450
    for line in explanation:
        c.drawString(50, y_pos, line)
        y_pos -= 15
    
    c.save()
    print("Overflow test PDF created: test_overflow_fix.pdf")

if __name__ == "__main__":
    test_overflow_prevention()
