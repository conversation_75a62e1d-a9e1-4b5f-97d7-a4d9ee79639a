#!/usr/bin/env python3
"""
Demo script showing the improved word wrapping behavior.
Long words are now moved to new lines instead of being broken.
"""

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from stitky import draw_text_with_breaks

def create_demo():
    """Create a demo PDF showing word wrapping behavior."""
    
    c = canvas.Canvas("demo_word_wrapping.pdf", pagesize=A4)
    width, height = A4
    
    # Title
    c.setFont("Helvetica-Bold", 16)
    c.drawString(50, height - 50, "Word Wrapping Demo - Long Words Move to New Lines")
    
    # Demo text with various scenarios
    demo_texts = [
        {
            "title": "Czech Address Example",
            "text": "Dlouhá-velmi-dlouhá-ulice-s-pomlčkami 123/456, Město-s-velmi-dlouhým-názvem 12345",
            "y_pos": height - 120
        },
        {
            "title": "Organization Name Example", 
            "text": "Sdružení-pro-podporu-dlouhých-názvů-organizací-v-České-republice o.p.s.",
            "y_pos": height - 220
        },
        {
            "title": "Mixed Content Example",
            "text": "Normal words with supercalifragilisticexpialidocious and antidisestablishmentarianism mixed in.",
            "y_pos": height - 320
        },
        {
            "title": "URL Example",
            "text": "Visit https://www.very-long-domain-name-that-does-not-fit-on-single-line.com for more info.",
            "y_pos": height - 420
        }
    ]
    
    # Draw each demo
    for demo in demo_texts:
        # Title
        c.setFont("Helvetica-Bold", 12)
        c.drawString(50, demo["y_pos"], demo["title"])
        
        # Draw box to show width constraint
        box_width = 300
        box_height = 60
        c.rect(50, demo["y_pos"] - box_height - 10, box_width, box_height, stroke=1, fill=0)
        
        # Draw text with wrapping
        c.setFont("Helvetica", 10)
        draw_text_with_breaks(
            c,
            demo["text"],
            55,  # x position (slightly inside the box)
            demo["y_pos"] - 25,  # y position
            box_width - 10,  # max width (box width minus padding)
            12,  # line height
            "Helvetica",
            10,
            demo["y_pos"] - box_height  # min_y boundary
        )
    
    # Add explanation
    c.setFont("Helvetica", 10)
    explanation = [
        "Key Features:",
        "• Long words are moved to new lines instead of being broken",
        "• Maintains word integrity and readability", 
        "• Handles Czech text with diacritics and hyphens properly",
        "• Works with URLs, addresses, and organization names",
        "• Automatically normalizes whitespace"
    ]
    
    y_pos = height - 550
    for line in explanation:
        c.drawString(50, y_pos, line)
        y_pos -= 15
    
    c.save()
    print("Demo PDF created: demo_word_wrapping.pdf")

if __name__ == "__main__":
    create_demo()
